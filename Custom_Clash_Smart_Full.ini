;Custom_OpenClash_Rules
;全分组防 DNS 泄漏订阅转换模板
;作者：https://github.com/Aethersailor
;项目地址：https://github.com/Aethersailor/Custom_OpenClash_Rules
;基于 ACL4SSR 模板魔改而来，感谢原作者！
;<必须>搭配本项目配套教程，实现最佳化的 OpenClash 使用效果！
;教程：https://github.com/Aethersailor/Custom_OpenClash_Rules/wiki/OpenClash-%E8%AE%BE%E7%BD%AE%E6%96%B9%E6%A1%88
;有问题可提 issue，或者加入本项目 Telegram 群组进行讨论
;Telegram 群组：https://t.me/custom_openclash_rules_group
;Telegram 通知频道：https://t.me/custom_openclash_rules

;本模板为 Full 模板，可以自由添加规则和分组，无性能考量，较为臃肿

[custom]
;设置规则标志位
;以下规则，按照从上往下的顺序遍历，优先命中上位规则，规则重复无影响
;修改顺序会影响分流效果

;本地地址和域名直连
ruleset=🎯 全球直连,[]GEOSITE,private
ruleset=🎯 全球直连,[]GEOIP,private,no-resolve
;本项目收录的直连规则
ruleset=🎯 全球直连,clash-classic:https://testingcf.jsdelivr.net/gh/Aethersailor/Custom_OpenClash_Rules@main/rule/Custom_Direct_Classical.yaml,28800
;本项目收录的代理规则
ruleset=🚀 手动选择,clash-classic:https://testingcf.jsdelivr.net/gh/Aethersailor/Custom_OpenClash_Rules@main/rule/Custom_Proxy_Classical.yaml,28800
;谷歌在国内可用的域名直连
ruleset=🎯 全球直连,[]GEOSITE,google-cn
;国内游戏域名直连
ruleset=🎯 全球直连,[]GEOSITE,category-games@cn
;Steam 下载 CDN 直连
ruleset=🎯 全球直连,clash-classic:https://testingcf.jsdelivr.net/gh/Aethersailor/Custom_OpenClash_Rules@main/rule/Steam_CDN_Classical.yaml,2880
;各大游戏平台下载域名直连
ruleset=🎯 全球直连,[]GEOSITE,category-game-platforms-download
;BT Tracker 相关域名直连
ruleset=🎯 全球直连,[]GEOSITE,category-public-tracker
;即时通讯包括了 Telegram/WhatsApp/Line 等海外主流即时通讯软件域名
ruleset=💬 即时通讯,[]GEOSITE,category-communication
;社交媒体包括了 Twitter(X)/Facebook/Instagram 等海外主流社交媒体
ruleset=🌐 社交媒体,[]GEOSITE,category-social-media-!cn
ruleset=📞 Talkatone,[]GEOSITE,talkatone
ruleset=🤖 ChatGPT,[]GEOSITE,openai
ruleset=💾 OneDrive,[]GEOSITE,onedrive
ruleset=🤖 Copilot,[]GEOSITE,bing
ruleset=🤖 AI服务,[]GEOSITE,category-ai-!cn
ruleset=🚀 GitHub,[]GEOSITE,github
;测速工具包括 SpeedTest 等主流测速工具域名
ruleset=🚀 测速工具,[]GEOSITE,category-speedtest
ruleset=🎮 Steam,[]GEOSITE,steam
ruleset=📹 YouTube,[]GEOSITE,youtube
ruleset=🎥 AppleTV+,[]GEOSITE,apple-tvplus
ruleset=🍎 苹果服务,[]GEOSITE,apple
ruleset=Ⓜ️ 微软服务,[]GEOSITE,microsoft
ruleset=📢 谷歌FCM,[]GEOSITE,googlefcm
ruleset=🇬 谷歌服务,[]GEOSITE,google
ruleset=🎶 TikTok,[]GEOSITE,tiktok
ruleset=🎥 Netflix,[]GEOSITE,netflix
ruleset=🎥 DisneyPlus,[]GEOSITE,disney
ruleset=🎥 HBO,[]GEOSITE,hbo
ruleset=🎥 PrimeVideo,[]GEOSITE,primevideo
;Emby 包括主流 Emby 服务相关域名
ruleset=🎥 Emby,[]GEOSITE,category-emby
ruleset=🎻 Spotify,[]GEOSITE,spotify
ruleset=📺 Bahamut,[]GEOSITE,bahamut
ruleset=🎮 游戏平台,[]GEOSITE,category-games
ruleset=🌎 国外媒体,[]GEOSITE,category-entertainment
ruleset=⏬ PT站点,[]GEOSITE,category-pt
ruleset=💳 PayPal,[]GEOSITE,paypal
ruleset=🛒 国外电商,[]GEOSITE,category-ecommerce
ruleset=🚀 手动选择,[]GEOSITE,gfw
ruleset=💬 即时通讯,[]GEOIP,telegram,no-resolve
ruleset=🌐 社交媒体,[]GEOIP,twitter,no-resolve
ruleset=🌐 社交媒体,[]GEOIP,facebook,no-resolve
ruleset=🇬 谷歌服务,[]GEOIP,google,no-resolve
ruleset=🎥 Netflix,[]GEOIP,netflix,no-resolve
;由于 OpenClash 使用的大陆白名单收录不全，此处留有 geosite:cn 作为国内域名兜底
ruleset=🎯 全球直连,[]GEOSITE,cn
;由于 OpenClash 使用的大陆白名单收录不全，此处留有 geoip:cn 作为国内 IP 兜底
ruleset=🎯 全球直连,[]GEOIP,cn,no-resolve
;以上兜底规则会根据实际情况随时取消
;PT/BT 优化开启会使 80/443 以外端口的流量直连
ruleset=🔀 非标端口,clash-classic:https://testingcf.jsdelivr.net/gh/Aethersailor/Custom_OpenClash_Rules@main/rule/Custom_Port_Direct.yaml,28800
;国内冷门域名会命中漏网之鱼，如影响使用，请设置漏网之鱼直连
;漏网之鱼直连时，无法通过 DNS 泄露测试，但是并不存在泄露
ruleset=🐟 漏网之鱼,[]FINAL
;设置规则标志位结束

;设置节点分组标志位
;节点地区分组参考本项目推荐机场而设立
;Smart 内核专用模板
custom_proxy_group=🚀 手动选择`select`[]♻️ 自动智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`.*
custom_proxy_group=♻️ 自动智能`smart`.*`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=💬 即时通讯`select`[]♻️ 自动智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择
custom_proxy_group=🌐 社交媒体`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=📞 Talkatone`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🚀 GitHub`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连
custom_proxy_group=🤖 ChatGPT`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🤖 Copilot`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🤖 AI服务`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🎶 TikTok`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=📹 YouTube`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🎥 Netflix`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🎥 DisneyPlus`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🎥 HBO`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🎥 PrimeVideo`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🎥 AppleTV+`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🎥 Emby`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🎻 Spotify`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=📺 Bahamut`select`[]🇼🇸 台湾智能`[]🚀 手动选择`[]🎯 全球直连
custom_proxy_group=🌎 国外媒体`select`[]🇸🇬 新加坡智能`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=💳 PayPal`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🛒 国外电商`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=📢 谷歌FCM`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连
custom_proxy_group=🇬 谷歌服务`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连
custom_proxy_group=🍎 苹果服务`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能
custom_proxy_group=Ⓜ️ 微软服务`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能
custom_proxy_group=💾 OneDrive`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能
custom_proxy_group=🎮 游戏平台`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能
custom_proxy_group=🎮 Steam`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=⏬ PT站点`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🚀 测速工具`select`[]🎯 全球直连`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`.*
custom_proxy_group=🐟 漏网之鱼`select`[]🇭🇰 香港智能`[]🇺🇸 美国智能`[]🇯🇵 日本智能`[]🇸🇬 新加坡智能`[]🇼🇸 台湾智能`[]🇰🇷 韩国智能`[]🇨🇦 加拿大智能`[]🇬🇧 英国智能`[]🇫🇷 法国智能`[]🇩🇪 德国智能`[]🇳🇱 荷兰智能`[]🇹🇷 土耳其智能`[]🏠 家宽智能`[]🌐 其他智能`[]🚀 手动选择`[]♻️ 自动智能`[]🎯 全球直连`.*
custom_proxy_group=🔀 非标端口`select`[]🐟 遵循规则`[]🎯 全球直连
custom_proxy_group=🇭🇰 香港智能`smart`(港|HK|hk|Hong Kong|HongKong|hongkong|深港)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇺🇸 美国智能`smart`(美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States|UnitedStates)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇯🇵 日本智能`smart`(日本|川日|东京|大阪|泉日|埼玉|沪日|深日|(?<!尼|-)日|JP|Japan|🇯🇵)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇸🇬 新加坡智能`smart`(新加坡|坡|狮城|SG|Singapore)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇼🇸 台湾智能`smart`(台|新北|彰化|TW|Taiwan)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇰🇷 韩国智能`smart`(KR|Korea|KOR|首尔|韩|韓)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇨🇦 加拿大智能`smart`(加拿大|Canada|渥太华|温哥华|卡尔加里)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇬🇧 英国智能`smart`(英国|Britain|United Kingdom|England|伦敦)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇫🇷 法国智能`smart`(法国|France|巴黎)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇩🇪 德国智能`smart`(德国|Germany|柏林|法兰克福)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇳🇱 荷兰智能`smart`(荷兰|Netherlands|阿姆斯特丹)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🇹🇷 土耳其智能`smart`(土耳其|Turkey|Türkiye)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🌐 其他智能`smart`(^(?!.*(港|HK|hk|Hong Kong|HongKong|hongkong|深港|美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States|UnitedStates|日本|川日|东京|大阪|泉日|埼玉|沪日|深日|(?<!尼|-)日|JP|Japan|🇯🇵|新加坡|坡|狮城|SG|Singapore|台|新北|彰化|TW|Taiwan|KR|Korea|KOR|首尔|韩|韓|加拿大|Canada|渥太华|温哥华|卡尔加里|英国|Britain|United Kingdom|England|伦敦|法国|France|巴黎|德国|Germany|柏林|法兰克福|荷兰|Netherlands|阿姆斯特丹|土耳其|Turkey|Türkiye|家宽|家庭宽带|住宅)).*)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🏠 家宽智能`smart`(家宽|家庭宽带|住宅)`https://www.gstatic.com/generate_204`300`uselightgbm=true`collectdata=true`strategy=sticky-sessions`policy-priority=""
custom_proxy_group=🎯 全球直连`select`[]DIRECT
custom_proxy_group=🐟 遵循规则`select`[]🐟 漏网之鱼
;设置分组标志位

;下方参数请勿修改
enable_rule_generator=true
overwrite_original_rules=true

